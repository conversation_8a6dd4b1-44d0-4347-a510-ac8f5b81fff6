package com.cashop.translate.web.controller;

import com.cashop.common.base.response.Result;
import com.cashop.translate.common.dto.TranslateRequest;
import com.cashop.translate.common.dto.TranslateResponse;
import com.cashop.translate.service.TranslateService;
import com.cashop.translate.facade.TranslateFacade;
import com.cashop.translate.facade.dto.*;
import com.cashop.translate.web.validation.AliyunTranslateParamValidator;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;


/**
 * 翻译服务
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/translate")
@Tag(name = "翻译服务", description = "翻译服务相关接口")
@Validated
public class TranslateController implements TranslateFacade {

    private static final Logger logger = LoggerFactory.getLogger(TranslateController.class);

    Gson gson = new Gson();

    @Autowired
    private TranslateService translateService;

    @Operation(summary = "同步单张图片翻译", description = "提供单张图片的同步翻译服务")
    @PostMapping("/image/sync")
    public Result<TranslateImageSyncResponse> translateImageSync(
            @Valid @RequestBody TranslateImageSyncRequest request) {

        logger.info("收到同步图片翻译请求，requestId: {}", request.getRequestId());

        // 参数校验
        if (request.getImageUrl() == null && request.getImageBase64() == null) {
            return Result.error("400", "图片URL或Base64编码不能为空");
        }

        if (request.getImageUrl() != null && request.getImageBase64() != null) {
            return Result.error("400", "图片URL和Base64编码只能选择其中一种");
        }

        try {
            // 转换请求对象
            TranslateRequest translateRequest = convertToTranslateRequest(request);

            // 调用服务（同步调用）
            TranslateResponse response = translateService.translateImageSync(translateRequest).get();

            if (response.isSuccess()) {
                TranslateImageSyncResponse syncResponse = convertToImageSyncResponse(request, response);
                return Result.success(syncResponse, "翻译请求处理成功");
            } else {
                return Result.error("500", response.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("同步图片翻译请求处理异常", e);
            return Result.error("请求处理异常: " + e.getMessage());
        }
    }

    @Operation(summary = "异步批量图片翻译", description = "提供多张图片的异步批量翻译服务")
    @PostMapping("/image/batch")
    public Result<TranslateImageBatchResponse> translateImageBatch(
            @Valid @RequestBody TranslateImageBatchRequest request) {

        logger.info("收到批量图片翻译请求，requestId: {}", request.getRequestId());

        // 参数校验
        if (CollectionUtils.isEmpty(request.getImageUrls()) && CollectionUtils.isEmpty(request.getImageBase64List())) {
            return Result.error("400", "图片URL或Base64编码不能为空");
        }

        // 批量翻译目前只支持URL方式
        if (CollectionUtils.isEmpty(request.getImageUrls())) {
            return Result.error("400", "批量翻译目前只支持图片URL方式");
        }

        if (request.getImageUrls().size() > 20) {
            return Result.error("400", "批量翻译最多支持20张图片");
        }

        try {
            // 转换请求对象
            TranslateRequest translateRequest = convertToTranslateRequest(request);

            // 调用服务（同步调用）
            TranslateResponse response = translateService.translateImageBatch(translateRequest).get();

            if (response.isSuccess()) {
                TranslateImageBatchResponse batchResponse = convertToImageBatchResponse(request, response);
                return Result.success(batchResponse, "批量翻译请求提交成功");
            } else {
                return Result.error("500", response.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("批量图片翻译请求处理异常", e);
            return Result.error("请求处理异常: " + e.getMessage());
        }
    }

    @Operation(summary = "获取批量翻译结果", description = "根据请求ID获取批量翻译的结果")
    @GetMapping("/image/batch/result")
    public Result<TranslateBatchResultResponse> getBatchTranslateResult(
            @Parameter(description = "请求ID", required = true)
            @NotBlank(message = "请求ID不能为空")
            @RequestParam String requestId) {
        
        logger.info("获取批量翻译结果，requestId: {}", requestId);

        try {
            TranslateResponse response = translateService.getBatchTranslateResult(requestId);
            
            if (response.isSuccess()) {
                TranslateBatchResultResponse resultResponse = convertToBatchResultResponse(response);
                return Result.success(resultResponse, "获取翻译结果成功");
            } else {
                return Result.error("404", response.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("获取批量翻译结果异常，requestId: {}", requestId, e);
            return Result.error("获取结果异常: " + e.getMessage());
        }
    }

    @Operation(summary = "同步文本翻译", description = "提供文本的同步翻译服务")
    @PostMapping("/text/sync")
    public Result<TranslateTextResponse> translateTextSync(
            @Valid @RequestBody TranslateTextRequest request) {

        logger.info("收到同步文本翻译请求，requestId: {}", request.getRequestId());
        long startTime = System.currentTimeMillis();

        try {
            // 转换请求对象
            TranslateRequest translateRequest = convertToTranslateRequest(request);

            // 调用服务（同步调用）
            TranslateResponse response = translateService.translateTextSync(translateRequest).get();
            long processingTime = System.currentTimeMillis() - startTime;

            if (response.isSuccess()) {
                TranslateTextResponse textResponse = convertToTextResponse(request, response, processingTime);
                return Result.success(textResponse, "文本翻译成功");
            } else {
                TranslateTextResponse textResponse = new TranslateTextResponse();
                textResponse.setRequestId(request.getRequestId());
                textResponse.setSuccess(false);
                textResponse.setErrorMessage(response.getErrorMessage());
                textResponse.setOriginalText(request.getText());
                textResponse.setSourceLanguage(request.getSourceLanguage());
                textResponse.setTargetLanguage(request.getTargetLanguage());
                textResponse.setProcessingTime(processingTime);
                return Result.error("500", response.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("同步文本翻译请求处理异常", e);
            return Result.error("请求处理异常: " + e.getMessage());
        }
    }

    /**
     * 将TranslateTextRequest转换为TranslateRequest
     */
    private TranslateRequest convertToTranslateRequest(TranslateTextRequest textRequest) {
        TranslateRequest translateRequest = new TranslateRequest();
        translateRequest.setRequestId(textRequest.getRequestId());
        translateRequest.setSourceLanguage(textRequest.getSourceLanguage());
        translateRequest.setTargetLanguage(textRequest.getTargetLanguage());
        translateRequest.setText(textRequest.getText());

        // 设置阿里云文本翻译特有参数，验证并使用有效值或默认值
        translateRequest.setFormatType(AliyunTranslateParamValidator.validateAndGetFormatType(textRequest.getFormatType()));
        translateRequest.setScene(AliyunTranslateParamValidator.validateAndGetScene(textRequest.getScene()));

        translateRequest.setExt(textRequest.getExt());
        return translateRequest;
    }

    /**
     * 将TranslateImageSyncRequest转换为TranslateRequest
     */
    private TranslateRequest convertToTranslateRequest(TranslateImageSyncRequest imageSyncRequest) {
        TranslateRequest translateRequest = new TranslateRequest();
        translateRequest.setRequestId(imageSyncRequest.getRequestId());
        translateRequest.setSourceLanguage(imageSyncRequest.getSourceLanguage());
        translateRequest.setTargetLanguage(imageSyncRequest.getTargetLanguage());

        // 处理单张图片
        if (imageSyncRequest.getImageUrl() != null) {
            translateRequest.setImageUrls(java.util.Arrays.asList(imageSyncRequest.getImageUrl()));
        }
        if (imageSyncRequest.getImageBase64() != null) {
            translateRequest.setImageBase64List(java.util.Arrays.asList(imageSyncRequest.getImageBase64()));
        }

        // 设置图片翻译场景，如果未指定则使用默认值
        String imageScene = imageSyncRequest.getScene() != null ? imageSyncRequest.getScene() : "general";
        translateRequest.setImageScene(imageScene);

        translateRequest.setExt(imageSyncRequest.getExt());
        return translateRequest;
    }

    /**
     * 将TranslateImageBatchRequest转换为TranslateRequest
     */
    private TranslateRequest convertToTranslateRequest(TranslateImageBatchRequest imageBatchRequest) {
        TranslateRequest translateRequest = new TranslateRequest();
        translateRequest.setRequestId(imageBatchRequest.getRequestId());
        translateRequest.setSourceLanguage(imageBatchRequest.getSourceLanguage());
        translateRequest.setTargetLanguage(imageBatchRequest.getTargetLanguage());
        translateRequest.setImageUrls(imageBatchRequest.getImageUrls());
        translateRequest.setImageBase64List(imageBatchRequest.getImageBase64List());

        // 设置图片翻译场景，如果未指定则使用默认值
        String imageScene = imageBatchRequest.getScene() != null ? imageBatchRequest.getScene() : "general";
        translateRequest.setImageScene(imageScene);

        translateRequest.setExt(imageBatchRequest.getExt());
        return translateRequest;
    }

    /**
     * 将TranslateResponse转换为TranslateTextResponse
     */
    private TranslateTextResponse convertToTextResponse(TranslateTextRequest request, TranslateResponse response, long processingTime) {
        TranslateTextResponse textResponse = new TranslateTextResponse();
        textResponse.setRequestId(response.getRequestId());
        textResponse.setSuccess(response.isSuccess());
        textResponse.setOriginalText(request.getText());
        textResponse.setSourceLanguage(request.getSourceLanguage());
        textResponse.setTargetLanguage(request.getTargetLanguage());
        textResponse.setProcessingTime(processingTime);
        textResponse.setTranslatedText(response.getTranslateTextResult());
        /*
        // 从阿里云响应中提取翻译结果
        if (response.isSuccess() && response.getCloudApiResponse() != null) {
            try {
                // 这里需要根据阿里云API的实际响应格式来解析翻译结果
                // 暂时使用简单的方式，实际项目中需要根据具体的响应格式来解析
                textResponse.setTranslatedText("翻译结果待解析"); // TODO: 解析实际的翻译结果
            } catch (Exception e) {
                logger.warn("解析翻译结果失败，requestId: {}", response.getRequestId(), e);
                textResponse.setTranslatedText("翻译结果解析失败");
            }
        }
        */
        return textResponse;
    }

    /**
     * 将TranslateResponse转换为TranslateImageSyncResponse
     */
    private TranslateImageSyncResponse convertToImageSyncResponse(TranslateImageSyncRequest request, TranslateResponse response) {
        TranslateImageSyncResponse syncResponse = new TranslateImageSyncResponse();
        syncResponse.setRequestId(response.getRequestId());
        syncResponse.setSuccess(response.isSuccess());
        syncResponse.setStatus(response.getRequestStatus());
        syncResponse.setCloudApiResponse(response.getCloudApiResponse());
        syncResponse.setErrorMessage(response.getErrorMessage());
        syncResponse.setSourceLanguage(request.getSourceLanguage());
        syncResponse.setTargetLanguage(request.getTargetLanguage());
        syncResponse.setImageUrl(request.getImageUrl());
        syncResponse.setImageBase64(request.getImageBase64());
        syncResponse.setTranslateImageSyncResultUrl(response.getTranslateImageSyncResultUrl());
        return syncResponse;
    }

    /**
     * 将TranslateResponse转换为TranslateImageBatchResponse
     */
    private TranslateImageBatchResponse convertToImageBatchResponse(TranslateImageBatchRequest request, TranslateResponse response) {
        TranslateImageBatchResponse batchResponse = new TranslateImageBatchResponse();
        batchResponse.setRequestId(response.getRequestId());
        batchResponse.setSuccess(response.isSuccess());
        batchResponse.setTaskId(response.getTaskId());
        batchResponse.setStatus(response.getRequestStatus());
        batchResponse.setCloudApiResponse(response.getCloudApiResponse());
        batchResponse.setErrorMessage(response.getErrorMessage());
        batchResponse.setSourceLanguage(request.getSourceLanguage());
        batchResponse.setTargetLanguage(request.getTargetLanguage());
        batchResponse.setImageUrls(request.getImageUrls());
        batchResponse.setImageCount(request.getImageUrls() != null ? request.getImageUrls().size() : 0);
        batchResponse.setScene(request.getScene());
        return batchResponse;
    }

    /**
     * 将TranslateResponse转换为TranslateBatchResultResponse
     */
    private TranslateBatchResultResponse convertToBatchResultResponse(TranslateResponse response) {
        TranslateBatchResultResponse resultResponse = new TranslateBatchResultResponse();
        resultResponse.setRequestId(response.getRequestId());
        resultResponse.setSuccess(response.isSuccess());
        resultResponse.setTaskId(response.getTaskId());
        resultResponse.setRequestStatus(response.getRequestStatus());
        resultResponse.setCloudApiStatus(response.getCloudApiStatus());
        resultResponse.setCloudApiResponse(response.getCloudApiResponse());
        resultResponse.setCloudApiAsyncStatus(response.getCloudApiAsyncStatus());
        resultResponse.setCloudApiAsyncResponse(response.getCloudApiAsyncResponse());
        resultResponse.setErrorMessage(response.getErrorMessage());
        resultResponse.setTranslateImageSyncResultUrl(response.getTranslateImageSyncResultUrl());
        if(null != response.getTranslateImageAsyncResults()) {
            resultResponse.setTranslateBatchResults(
                gson.fromJson(gson.toJson(response.getTranslateImageAsyncResults()), 
                    new TypeToken<List<TranslateBatchResult>>() {}.getType())
            );
        }
        resultResponse.setTranslatedText(response.getTranslateTextResult());
        return resultResponse;
    }
}
