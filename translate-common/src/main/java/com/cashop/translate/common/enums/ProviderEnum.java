package com.cashop.translate.common.enums;

/**
 * 云服务提供商枚举
 * 
 * <AUTHOR>
 */
public enum ProviderEnum {

    /**
     * 阿里云
     */
    ALIYUN("ALIYUN", "阿里云"),

    /**
     * 百度云
     */
    BAIDU("BAIDU", "百度云"),

    /**
     * 腾讯云
     */
    TENCENT("TENCENT", "腾讯云"),

    /**
     * 华为云
     */
    HUAWEI("HUAWEI", "华为云");

    private final String code;
    private final String description;

    ProviderEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static ProviderEnum fromCode(String code) {
        for (ProviderEnum provider : values()) {
            if (provider.getCode().equals(code)) {
                return provider;
            }
        }
        throw new IllegalArgumentException("Unknown provider code: " + code);
    }
}
