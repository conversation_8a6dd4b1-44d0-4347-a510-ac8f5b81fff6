package com.cashop.translate.dao.entity;

import java.time.LocalDateTime;

/**
 * 翻译请求记录实体
 * 
 * <AUTHOR>
 */
public class TranslateRequestRecord {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 请求ID，用于去重和查询
     */
    private String requestId;

    /**
     * 请求类型：SYNC_SINGLE(同步单张), ASYNC_BATCH(异步批量)
     */
    private String requestType;

    /**
     * 云服务提供商：ALIYUN, BAIDU, TENCENT, HUAWEI
     */
    private String provider;

    /**
     * 源语言
     */
    private String sourceLanguage;

    /**
     * 目标语言
     */
    private String targetLanguage;

    /**
     * 图片数量
     */
    private Integer imageCount;

    /**
     * 请求参数JSON
     */
    private String requestParams;

    /**
     * 请求状态：PENDING(待处理), PROCESSING(处理中), SUCCESS(成功), FAILED(失败)
     */
    private String requestStatus;

    /**
     * 云服务API状态：SUCCESS(成功), FAILED(失败)
     */
    private String cloudApiStatus;

    /**
     * 云服务API响应JSON
     */
    private String cloudApiResponse;

    /**
     * 异步查询结果-云服务API状态：SUCCESS(成功), FAILED(失败), PROCESSING(处理中)
     */
    private String cloudApiAsyncStatus;

    /**
     * 异步查询结果-云服务API响应JSON
     */
    private String cloudApiAsyncResponse;

    /**
     * 阿里云批量翻译任务ID
     */
    private String taskId;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 错误信息
     */
    private String errorMessage;



    /**
     * 图片同步翻译结果URL
     */
    private String imageSyncResultUrl;

    /**
     * 图片批量翻译结果
     */
    private String imageBatchResults;

    /**
     * 文本翻译结果
     */
    private String translatedText;


    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getSourceLanguage() {
        return sourceLanguage;
    }

    public void setSourceLanguage(String sourceLanguage) {
        this.sourceLanguage = sourceLanguage;
    }

    public String getTargetLanguage() {
        return targetLanguage;
    }

    public void setTargetLanguage(String targetLanguage) {
        this.targetLanguage = targetLanguage;
    }

    public Integer getImageCount() {
        return imageCount;
    }

    public void setImageCount(Integer imageCount) {
        this.imageCount = imageCount;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    public String getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(String requestStatus) {
        this.requestStatus = requestStatus;
    }

    public String getCloudApiStatus() {
        return cloudApiStatus;
    }

    public void setCloudApiStatus(String cloudApiStatus) {
        this.cloudApiStatus = cloudApiStatus;
    }

    public String getCloudApiResponse() {
        return cloudApiResponse;
    }

    public void setCloudApiResponse(String cloudApiResponse) {
        this.cloudApiResponse = cloudApiResponse;
    }

    public String getCloudApiAsyncStatus() {
        return cloudApiAsyncStatus;
    }

    public void setCloudApiAsyncStatus(String cloudApiAsyncStatus) {
        this.cloudApiAsyncStatus = cloudApiAsyncStatus;
    }

    public String getCloudApiAsyncResponse() {
        return cloudApiAsyncResponse;
    }

    public void setCloudApiAsyncResponse(String cloudApiAsyncResponse) {
        this.cloudApiAsyncResponse = cloudApiAsyncResponse;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }

    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getImageSyncResultUrl() {
        return imageSyncResultUrl;
    }

    public void setImageSyncResultUrl(String imageSyncResultUrl) {
        this.imageSyncResultUrl = imageSyncResultUrl;
    }

    public String getImageBatchResults() {
        return imageBatchResults;
    }

    public void setImageBatchResults(String imageBatchResults) {
        this.imageBatchResults = imageBatchResults;
    }

    public String getTranslatedText() {
        return translatedText;
    }

    public void setTranslatedText(String translatedText) {
        this.translatedText = translatedText;
    }

    
}
