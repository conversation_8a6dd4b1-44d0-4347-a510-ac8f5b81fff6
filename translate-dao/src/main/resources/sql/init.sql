-- 翻译请求记录表
CREATE TABLE `translate_request_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求ID，用于去重和查询',
  `request_type` varchar(20) NOT NULL COMMENT '请求类型：SYNC_SINGLE(同步单张), ASYNC_BATCH(异步批量)',
  `provider` varchar(20) NOT NULL COMMENT '云服务提供商：ALIYUN, BAIDU, TENCENT, HUAWEI',
  `source_language` varchar(10) DEFAULT NULL COMMENT '源语言',
  `target_language` varchar(10) NOT NULL COMMENT '目标语言',
  `image_count` int(11) DEFAULT 1 COMMENT '图片数量',
  `request_params` text COMMENT '请求参数JSON',
  `request_status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '请求状态：PENDING(待处理), PROCESSING(处理中), SUCCESS(成功), FAILED(失败)',
  `cloud_api_status` varchar(20) DEFAULT NULL COMMENT '云服务API状态：SUCCESS(成功), FAILED(失败)',
  `cloud_api_response` text COMMENT '云服务API响应JSON',
  `cloud_api_async_status` varchar(20) DEFAULT NULL COMMENT '异步查询结果-云服务API状态：SUCCESS(成功), FAILED(失败), PROCESSING(处理中)',
  `cloud_api_async_response` text COMMENT '异步查询结果-云服务API响应JSON',
  `task_id` varchar(64) DEFAULT NULL COMMENT '阿里云批量翻译任务ID',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `max_retry_count` int(11) DEFAULT 3 COMMENT '最大重试次数',
  `error_message` text COMMENT '错误信息',
  `image_sync_result_url` varchar(128) COMMENT '图片同步翻译结果URL',
  `image_batch_results` text COMMENT '图片批量翻译结果',
  `translated_text` text COMMENT '文本翻译结果',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_id` (`request_id`),
  KEY `idx_request_type` (`request_type`),
  KEY `idx_provider` (`provider`),
  KEY `idx_request_status` (`request_status`),
  KEY `idx_cloud_api_async_status` (`cloud_api_async_status`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='翻译请求记录表';
