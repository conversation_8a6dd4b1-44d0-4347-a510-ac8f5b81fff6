package com.cashop.translate.client.aliyun;

import org.junit.jupiter.api.Test;

import com.cashop.translate.client.yiketu.YiKeTuTranslateTest;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * YiKeTuTranslateTest 测试类
 * 
 * <AUTHOR>
 */
public class YiKeTuTranslateTest {

    private final YiKeTuTranslateTest client = new YiKeTuTranslateTest();

    @Test
    public void testSign() {
        // 测试用例：根据文档提供的示例
        Map<String, Object> params = new HashMap<>();
        params.put("timestamp", "1640966400");
        params.put("appKey", "5690032671");
        params.put("pageNo", "1");
        params.put("pageSize", "1");
        
        String appSecret = "YOUR_APPSECRET";
        String expectedSign = "A337D461E3EF51F4FE1EC6F3A7FF2D3B";
        
        String actualSign = client.sign(params, appSecret);
        
        assertEquals(expectedSign, actualSign, "签名结果不匹配");
    }

    @Test
    public void testSignWithBooleanValue() {
        // 测试布尔值转换
        Map<String, Object> params = new HashMap<>();
        params.put("timestamp", "1640966400");
        params.put("appKey", "5690032671");
        params.put("enabled", true);
        params.put("disabled", false);
        
        String appSecret = "TEST_SECRET";
        String actualSign = client.sign(params, appSecret);
        
        assertNotNull(actualSign);
        assertEquals(32, actualSign.length()); // MD5结果应该是32位
        assertTrue(actualSign.matches("[A-F0-9]+"), "签名应该是大写的十六进制字符串");
    }

    @Test
    public void testSignWithSignParameter() {
        // 测试包含sign参数的情况，应该被过滤掉
        Map<String, Object> params = new HashMap<>();
        params.put("timestamp", "1640966400");
        params.put("appKey", "5690032671");
        params.put("sign", "SHOULD_BE_IGNORED");
        
        String appSecret = "TEST_SECRET";
        String actualSign = client.sign(params, appSecret);
        
        assertNotNull(actualSign);
        assertEquals(32, actualSign.length());
    }

    @Test
    public void testSignWithNullParams() {
        // 测试空参数
        String appSecret = "TEST_SECRET";
        
        assertThrows(IllegalArgumentException.class, () -> {
            client.sign(null, appSecret);
        });
    }

    @Test
    public void testSignWithNullAppSecret() {
        // 测试空密钥
        Map<String, Object> params = new HashMap<>();
        params.put("test", "value");
        
        assertThrows(IllegalArgumentException.class, () -> {
            client.sign(params, null);
        });
    }

    @Test
    public void testSignWithEmptyParams() {
        // 测试空参数Map
        Map<String, Object> params = new HashMap<>();
        String appSecret = "TEST_SECRET";
        
        String actualSign = client.sign(params, appSecret);
        
        assertNotNull(actualSign);
        assertEquals(32, actualSign.length());
    }

    @Test
    public void testSignParameterOrdering() {
        // 测试参数排序是否正确
        Map<String, Object> params1 = new HashMap<>();
        params1.put("z", "1");
        params1.put("a", "2");
        params1.put("m", "3");
        
        Map<String, Object> params2 = new HashMap<>();
        params2.put("a", "2");
        params2.put("m", "3");
        params2.put("z", "1");
        
        String appSecret = "TEST_SECRET";
        String sign1 = client.sign(params1, appSecret);
        String sign2 = client.sign(params2, appSecret);
        
        assertEquals(sign1, sign2, "相同参数不同顺序应该产生相同的签名");
    }
}
